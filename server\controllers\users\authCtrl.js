/**
 * Authentication Controller
 *
 * Handles user authentication, registration, profile management, and password reset functionality.
 * Implements security best practices including:
 * - Account lockout after multiple failed login attempts
 * - Secure password reset flow
 * - Session management
 * - Audit logging for security events
 */

const User = require("../../models/users/userModel");
const OTP = require("../../models/utils/otpModel");
const Admin = require("../../models/users/adminModel");
const asyncHandler = require("express-async-handler");
const { generateToken } = require("../../config/jwtToken");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const { generateRefreshToken } = require("../../config/refreshToken");
const validateUser = require("../../middlewares/validateUser");
const bcrypt = require("bcryptjs");
const sendEmail = require("../utils/emailCtrl");
const crypto = require("crypto");
const formidable = require("formidable");
const cloudinary = require("cloudinary").v2;
const jwt = require("jsonwebtoken");
const { logAuthEvent } = require("../../utils/auditLogger");
const { createSession } = require("../utils/sessionCtrl");
const { checkSuspiciousActivity } = require("../../utils/securityUtils");
// const speakeasy = require("speakeasy");
// const qrcode = require("qrcode");

// const validateUserRegister = asyncHandler(async (req, res) => {
//   const { fullname, username, mobile, email, password, confirmPassword } =
//     req.body;

//   // Check if all required fields are provided
//   if (
//     !fullname ||
//     !username ||
//     !mobile ||
//     !email ||
//     !password ||
//     !confirmPassword
//   ) {
//     return res.status(400).json("All fields are required");
//   }

//   // Validate fullname
//   if (fullname.trim().length < 3) {
//     return res.status(400).json("Full name must be at least 3 characters long");
//   }

//   // Validate username format
//   if (username.length < 3 || username.length > 12) {
//     return res
//       .status(400)
//       .json("Username must be between 3 and 12 characters long");
//   }

//   // Check if username already exists
//   const userExistsByUsername = await User.findOne({ username: username });
//   const userExistsByUsernameOnAdmin = await Admin.findOne({
//     username: username,
//   });
//   if (userExistsByUsername || userExistsByUsernameOnAdmin) {
//     return res.status(400).json("Username already exists");
//   }

//   // Validate email format
//   if (!/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(email)) {
//     return res.status(400).json("Please input a valid email address");
//   }

//   // Check if email already exists
//   const userExistsByEmail = await User.findOne({ email: email });
//   const userExistOnAdmin = await Admin.findOne({ email: email });
//   if (userExistsByEmail || userExistOnAdmin) {
//     return res.status(400).json("Email already registered");
//   }

//   // Validate mobile format
//   if (!/^\d{9}$/.test(mobile)) {
//     return res.status(400).json("Mobile number must contain exactly 9 digits");
//   }

//   // Check if mobile already exists
//   const userExistsByMobile = await User.findOne({ mobile: mobile });
//   const userExistsByMobileOnAdmin = await Admin.findOne({ mobile: mobile });
//   if (userExistsByMobile || userExistsByMobileOnAdmin) {
//     return res.status(400).json("Mobile number already registered");
//   }

//   // Validate password complexity
//   if (
//     password.length < 8 ||
//     !/[A-Z]/.test(password) ||
//     !/[a-z]/.test(password) ||
//     !/\d/.test(password) ||
//     !/[!@#$%^&*]/.test(password)
//   ) {
//     return res
//       .status(400)
//       .json(
//         "Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character"
//       );
//   }

//   // Check if passwords match
//   if (password !== confirmPassword) {
//     return res.status(400).json("Passwords do not match");
//   }

//   // All validations passed
//   return res.status(200).json("validation successfully passed");
// });

const validateUserRegister = asyncHandler(async (req, res) => {
  const { fullname, username, mobile, email, password, confirmPassword } =
    req.body;

  // Enhanced null, undefined, and empty string validation
  const requiredFields = {
    fullname,
    username,
    mobile,
    email,
    password,
    confirmPassword,
  };
  
  for (const [field, value] of Object.entries(requiredFields)) {
    if (
      value === null ||
      value === undefined ||
      value === "" ||
      (typeof value === "string" && value.trim() === "") ||
      (typeof value === "string" && value.length === 0)
    ) {
      return res.status(400).json("All fields are required");
    }
  }

  // Enhanced data type validation with length checks
  if (
    typeof fullname !== "string" ||
    typeof username !== "string" ||
    typeof mobile !== "string" ||
    typeof email !== "string" ||
    typeof password !== "string" ||
    typeof confirmPassword !== "string"
  ) {
    return res.status(400).json("All fields must be strings");
  }

  // Enhanced fullname validation
  const trimmedFullname = fullname.trim();
  if (trimmedFullname.length < 3) {
    return res.status(400).json("Full name must be at least 3 characters long");
  }

  // Check for excessive whitespace
  if (/\s{3,}/.test(trimmedFullname)) {
    return res.status(400).json("Full name cannot contain excessive whitespace");
  }

  // Check for excessive input lengths to prevent DoS attacks
  const maxLengths = {
    fullname: 50, // Reduced from 100 for better UX
    username: 12,
    mobile: 9,
    email: 254, // RFC 5321 standard
    password: 128,
  };

  for (const [field, maxLength] of Object.entries(maxLengths)) {
    if (req.body[field] && req.body[field].length > maxLength) {
      return res.status(400).json(`${field} cannot exceed ${maxLength} characters`);
    }
  }

  // Enhanced XSS and injection prevention
  const dangerousPatterns = [
    /[<>\"'&]/g, // Basic XSS
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT|JAVASCRIPT|ONLOAD|ONERROR|ONCLICK)\b)/gi, // SQL/JS injection
    /(javascript|vbscript|expression|applet|meta|xml|blink|link|style|embed|object|iframe|frame|frameset|noframes|noscript)/gi, // HTML injection
    /(eval|setTimeout|setInterval|Function|constructor|prototype|__proto__|window|document|location|history|navigator)/gi, // JS injection
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(trimmedFullname)) {
      return res.status(400).json("Full name contains prohibited content");
    }
  }

  // Enhanced username validation
  if (username.length < 3 || username.length > 12) {
    return res
      .status(400)
      .json("Username must be between 3 and 12 characters long");
  }

  // Enhanced username character restrictions
  if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    return res
      .status(400)
      .json(
        "Username can only contain letters, numbers, underscores, and hyphens"
      );
  }

  // Prevent username from starting or ending with special characters
  if (!/^[a-zA-Z0-9]/.test(username) || !/[a-zA-Z0-9]$/.test(username)) {
    return res.status(400).json("Username must start and end with a letter or number");
  }

  // Enhanced reserved usernames list
  const reservedUsernames = [
    "admin", "root", "administrator", "system", "api", "www", "mail", "ftp",
    "support", "help", "info", "contact", "sales", "billing", "security",
    "test", "demo", "guest", "anonymous", "null", "undefined", "true", "false",
    "onprintz", "onprint", "print", "user", "users", "account", "accounts",
    "login", "logout", "register", "signup", "signin", "auth", "authentication"
  ];
  
  if (reservedUsernames.includes(username.toLowerCase())) {
    return res.status(400).json("Username is reserved and cannot be used");
  }

  // Check for username patterns that might be used for impersonation
  if (/^(admin|mod|staff|support|help|info|contact|sales|billing|security)\d*$/i.test(username)) {
    return res.status(400).json("Username pattern is not allowed");
  }

  // Check if username already exists (case-insensitive)
  const userExistsByUsername = await User.findOne({
    username: { $regex: new RegExp(`^${username}$`, "i") },
  });
  const userExistsByUsernameOnAdmin = await Admin.findOne({
    username: { $regex: new RegExp(`^${username}$`, "i") },
  });
  if (userExistsByUsername || userExistsByUsernameOnAdmin) {
    return res.status(400).json("Username already exists");
  }

  // Enhanced email validation
  const trimmedEmail = email.trim().toLowerCase();

  // More comprehensive email regex with additional checks
  const emailRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  if (!emailRegex.test(trimmedEmail)) {
    return res.status(400).json("Please input a valid email address");
  }

  // Check for email format issues
  const emailParts = trimmedEmail.split('@');
  if (emailParts.length !== 2) {
    return res.status(400).json("Invalid email format");
  }

  const localPart = emailParts[0];
  const domainPart = emailParts[1];

  // Check local part length
  if (localPart.length === 0 || localPart.length > 64) {
    return res.status(400).json("Invalid email format");
  }

  // Check domain part
  if (domainPart.length === 0 || domainPart.length > 253) {
    return res.status(400).json("Invalid email format");
  }

  // Enhanced disposable email addresses list
  const disposableDomains = [
    "10minutemail.com", "tempmail.org", "guerrillamail.com", "mailinator.com",
    "throwaway.email", "temp-mail.org", "getnada.com", "maildrop.cc",
    "yopmail.com", "trashmail.com", "sharklasers.com", "guerrillamailblock.com",
    "pokemail.net", "spam4.me", "bccto.me", "chacuo.net", "dispostable.com",
    "fakeinbox.com", "mailnesia.com", "mailmetrash.com", "tempr.email",
    "tmpeml.com", "tmpmail.org", "tmpmail.net", "tmpeml.com", "tmpmail.io",
    "mailinator.net", "mailinator.org", "mailinator.info", "mailinator.biz"
  ];
  
  if (disposableDomains.includes(domainPart)) {
    return res.status(400).json("Disposable email addresses are not allowed");
  }

  // Check for suspicious email patterns
  if (/^(test|demo|admin|root|user|guest|anonymous|temp|fake|spam|bot)\d*@/i.test(trimmedEmail)) {
    return res.status(400).json("Email pattern is not allowed");
  }

  // Check if email already exists (case-insensitive)
  const userExistsByEmail = await User.findOne({
    email: { $regex: new RegExp(`^${trimmedEmail}$`, "i") },
  });
  const userExistOnAdmin = await Admin.findOne({
    email: { $regex: new RegExp(`^${trimmedEmail}$`, "i") },
  });
  if (userExistsByEmail || userExistOnAdmin) {
    return res.status(400).json("Email already registered");
  }

  // Enhanced mobile validation
  if (!/^\d{9}$/.test(mobile)) {
    return res.status(400).json("Please provide a valid mobile number");
  }

  // Enhanced fake mobile number patterns
  const fakeMobilePatterns = [
    "000000000", "111111111", "123456789", "987654321",
    "123123123", "456456456", "789789789", "012345678",
    "111111111", "222222222", "333333333", "444444444",
    "555555555", "666666666", "777777777", "888888888", "999999999"
  ];
  
  if (fakeMobilePatterns.includes(mobile)) {
    return res.status(400).json("Please provide a valid mobile number");
  }

  // Check if mobile already exists
  const userExistsByMobile = await User.findOne({ mobile: mobile });
  const userExistsByMobileOnAdmin = await Admin.findOne({ mobile: mobile });
  if (userExistsByMobile || userExistsByMobileOnAdmin) {
    return res.status(400).json("Mobile number already registered");
  }

  // Enhanced password validation
  if (password.length < 8) {
    return res.status(400).json("Password must be at least 8 characters long");
  }

  if (password.length > 128) {
    return res.status(400).json("Password cannot exceed 128 characters");
  }

  // Enhanced character set requirements
  if (!/[A-Z]/.test(password)) {
    return res
      .status(400)
      .json("Password must contain at least one uppercase letter");
  }
  if (!/[a-z]/.test(password)) {
    return res
      .status(400)
      .json("Password must contain at least one lowercase letter");
  }
  if (!/\d/.test(password)) {
    return res.status(400).json("Password must contain at least one number");
  }
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return res
      .status(400)
      .json("Password must contain at least one special character");
  }

  // Enhanced dictionary attack prevention
  const commonPasswords = [
    "password", "password123", "123456789", "qwerty123", "admin123",
    "Password123!", "password1!", "Welcome123!", "Qwerty123!", "letmein123",
    "monkey123", "dragon123", "master123", "shadow123", "football123",
    "baseball123", "welcome123", "login123", "abc123", "12345678",
    "qwerty", "1234567890", "1234567", "princess", "qwertyuiop",
    "admin", "welcome", "password1", "123123", "123456", "123456789",
    "qwerty", "abc123", "111111", "1234567", "dragon", "master",
    "monkey", "letmein", "login", "princess", "qwertyuiop", "solo",
    "passw0rd", "starwars", "freedom", "whatever", "qazwsx", "trustno1"
  ];
  
  if (
    commonPasswords.some((common) =>
      password.toLowerCase().includes(common.toLowerCase())
    )
  ) {
    return res
      .status(400)
      .json("Password is too common, please choose a stronger password");
  }

  // Personal information in password
  if (password.toLowerCase().includes(username.toLowerCase())) {
    return res.status(400).json("Password cannot contain username");
  }

  const emailLocal = trimmedEmail.split("@")[0];
  if (password.toLowerCase().includes(emailLocal.toLowerCase())) {
    return res.status(400).json("Password cannot contain email");
  }

  // Check if passwords match
  if (password !== confirmPassword) {
    return res.status(400).json("Passwords do not match");
  }

  // Rate limiting check (basic implementation)
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;
  const rateLimitKey = `register_attempts:${clientIp}`;
  
  // This would typically use Redis or a similar cache
  // For now, we'll just log the attempt
  console.log(`Registration attempt from IP: ${clientIp}`);

  // All validations passed
  return res.status(200).json("validation successfully passed");
});

const registerUser = asyncHandler(async (req, res) => {
  // Use formidable to handle multipart form data (for profile image)
  const form = new formidable.IncomingForm();
  form.keepExtensions = true;

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({
        success: false,
        error: "File parsing error",
        details: err.message,
      });
    }

    // Extract fields from the form data
    const fullname = fields.fullname
      ? Array.isArray(fields.fullname)
        ? fields.fullname[0]
        : fields.fullname
      : "";
    const username = fields.username
      ? Array.isArray(fields.username)
        ? fields.username[0]
        : fields.username
      : "";
    const mobile = fields.mobile
      ? Array.isArray(fields.mobile)
        ? fields.mobile[0]
        : fields.mobile
      : "";
    const email = fields.email
      ? Array.isArray(fields.email)
        ? fields.email[0]
        : fields.email
      : "";
    const password = fields.password
      ? Array.isArray(fields.password)
        ? fields.password[0]
        : fields.password
      : "";
    const confirmPassword = fields.confirmPassword
      ? Array.isArray(fields.confirmPassword)
        ? fields.confirmPassword[0]
        : fields.confirmPassword
      : "";
    const otp = fields.otp
      ? Array.isArray(fields.otp)
        ? fields.otp[0]
        : fields.otp
      : "";

    // Validate required fields
    if (!fullname || !username || !mobile || !email || !password || !otp) {
      return res.status(403).json({
        success: false,
        message: "All fields are required",
      });
    }

    if (password !== confirmPassword) {
      return res.status(403).json({
        success: false,
        message: "Passwords do not match",
      });
    }

    try {
      const userExists = await User.findOne({ email: email });
      if (userExists) {
        return res.status(400).json({
          success: false,
          message: "Email already exists",
        });
      }

      // Verify OTP
      const response = await OTP.find({ email })
        .sort({ createdAt: -1 })
        .limit(1);

      if (response.length === 0 || otp !== response[0].otp) {
        return res.status(400).json({
          success: false,
          message: "The OTP is not valid",
        });
      }

      // Prepare user data
      const userData = {
        fullname,
        username,
        email,
        mobile,
        password,
        loginAttempts: 0,
      };

      // Handle profile image upload if provided
      if (files.profile) {
        const profileFile = Array.isArray(files.profile)
          ? files.profile[0]
          : files.profile;

        try {
          // Upload to cloudinary
          const result = await cloudinary.uploader.upload(
            profileFile.filepath,
            {
              folder: "profiles",
              resource_type: "image",
            }
          );

          if (result) {
            userData.profile = result.secure_url;
          }
        } catch (uploadError) {
          console.error("Profile image upload error:", uploadError);
          // Continue with registration even if image upload fails
        }
      }

      // Create new user
      const newUser = await User.create(userData);

      // Log registration event
      const clientIp =
        req.headers["x-forwarded-for"] || req.socket.remoteAddress;
      logAuthEvent({
        action: "user_registered",
        user: newUser,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
        },
      });

      // Return user data (excluding sensitive fields)
      res.status(201).json({
        success: true,
        message: "User registered successfully",
        user: {
          _id: newUser._id,
          fullname: newUser.fullname,
          username: newUser.username,
          email: newUser.email,
          mobile: newUser.mobile,
          profile: newUser.profile,
        },
      });
    } catch (error) {
      // Handle duplicate key errors
      if (error.code === 11000) {
        if (error.keyPattern.username) {
          return res.status(400).json({
            success: false,
            message: "Username already exists",
          });
        } else if (error.keyPattern.mobile) {
          return res.status(400).json({
            success: false,
            message: "Mobile is already registered",
          });
        }
      }

      // Log registration failure
      const clientIp =
        req.headers["x-forwarded-for"] || req.socket.remoteAddress;
      logAuthEvent({
        action: "user_registration_failed",
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          error: error.message,
          email: email,
        },
      });

      // Return error response
      return res.status(500).json({
        success: false,
        message: "Registration failed",
        error: error.message,
      });
    }
  });
});

const loginUser = asyncHandler(async (req, res) => {
  const { email, password } = req.body;
  const findUser = await User.findOne({ email }).select("+loginAttempts");

  // Get client IP address for logging
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  // Check if the user is locked out
  if (findUser && findUser.lockUntil && findUser.lockUntil > Date.now()) {
    return res.status(403).json({
      message: `Account is locked. Try again after ${Math.ceil(
        (findUser.lockUntil - Date.now()) / 60000
      )} minutes.`,
    });
  }

  if (findUser) {
    if (await findUser.isPasswordMatched(password)) {
      // Reset login attempts and lock status on successful login
      findUser.loginAttempts = 0;
      findUser.lockUntil = null;

      // Update last login information
      findUser.lastLoginIp = clientIp;
      findUser.lastLoginAt = new Date();

      await findUser.save();

      // User type for this login
      const userType = "user";

      // Generate tokens with user type
      const accessToken = generateToken(findUser?._id, userType);
      const refreshToken = generateRefreshToken(findUser?._id, userType);

      // Update refresh token in database
      await User.findByIdAndUpdate(
        findUser.id,
        { refreshToken: refreshToken },
        { new: true }
      );

      // Create a new session
      const session = await createSession({
        userId: findUser._id,
        userModel: "User",
        refreshToken: refreshToken,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        expiresInDays: 3,
      });

      // Set type-specific refresh token as HTTP-only cookie
      res.cookie(`${userType}RefreshToken`, refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production", // Use secure in production
        sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
        maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the JWT expiration)
      });

      // Set type-specific access token as HTTP-only cookie
      res.cookie(`${userType}AccessToken`, accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
        maxAge: 24 * 60 * 60 * 1000, // 24 hours (matching the JWT expiration)
      });

      // Set type-specific session ID cookie (not HTTP-only so it can be accessed by client for session management)
      res.cookie(`${userType}SessionId`, session._id.toString(), {
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
        maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the refresh token)
      });

      // Also set generic cookies for backward compatibility
      res.cookie("refreshToken", refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
        maxAge: 72 * 60 * 60 * 1000,
      });

      res.cookie("accessToken", accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
        maxAge: 24 * 60 * 60 * 1000,
      });

      res.cookie("sessionId", session._id.toString(), {
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
        maxAge: 72 * 60 * 60 * 1000,
      });

      // Log successful login
      logAuthEvent({
        action: "login_success",
        user: findUser,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
          method: "password",
        },
      });

      // Check for suspicious activity after successful login
      await checkSuspiciousActivity({
        user: findUser,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        userModel: "User",
      });

      // Return user data and also include token in response for backward compatibility
      return res.json({
        message: "logged in successfully",
        _id: findUser?._id,
        name: findUser?.fullname,
        username: findUser?.username,
        email: findUser?.email,
        mobile: findUser?.mobile,
        preference: findUser.preference,
        profile: findUser?.profile,
        token: accessToken, // Include token in response for backward compatibility
        loginAttempts: 0,
        lastLogin: findUser.lastLoginAt,
      });
    } else {
      // Increment login attempts on failed login
      findUser.loginAttempts = (findUser.loginAttempts || 0) + 1;

      // Implement progressive lockout strategy
      if (findUser.loginAttempts >= 10) {
        if (findUser.loginAttempts % 10 === 0) {
          const lockTime = findUser.loginAttempts / 10;
          findUser.lockUntil = Date.now() + lockTime * 5 * 60 * 1000;

          // Log account lockout
          logAuthEvent({
            action: "account_locked",
            user: findUser,
            ipAddress: clientIp,
            userAgent: req.headers["user-agent"],
            status: "warning",
            details: {
              timestamp: new Date(),
              lockDuration: lockTime * 5 * 60 * 1000,
              loginAttempts: findUser.loginAttempts,
            },
          });
        }
      }

      await findUser.save();

      // Log failed login
      logAuthEvent({
        action: "login_failure",
        user: findUser,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          loginAttempts: findUser.loginAttempts,
          remainingAttempts: 10 - (findUser.loginAttempts % 10),
        },
      });

      return res.status(401).json({
        message: "Invalid Credentials",
        loginAttempts: findUser.loginAttempts,
        remainingAttempts: 10 - (findUser.loginAttempts % 10),
      });
    }
  } else {
    return res.status(401).json({ message: "Invalid Credentials" });
  }
});

const logout = asyncHandler(async (req, res) => {
  const cookie = req.cookies;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;
  const Session = require("../../models/utils/sessionModel");

  const userType = "user";

  // Clear type-specific cookies
  res.clearCookie(`${userType}RefreshToken`, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
  });

  res.clearCookie(`${userType}AccessToken`, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
  });

  res.clearCookie(`${userType}SessionId`, {
    httpOnly: false,
    secure: process.env.NODE_ENV === "production",
    sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
  });

  // Also clear generic cookies for backward compatibility
  res.clearCookie("refreshToken", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
  });

  res.clearCookie("accessToken", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
  });

  res.clearCookie("sessionId", {
    httpOnly: false,
    secure: process.env.NODE_ENV === "production",
    sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
  });

  // If there's a refresh token, clear it from the user record
  if (cookie?.refreshToken) {
    const refreshToken = cookie.refreshToken;
    const user = await User.findOne({ refreshToken });

    if (user) {
      // Clear the refresh token in the database
      user.refreshToken = "";
      await user.save();

      // Invalidate the session if it exists
      if (cookie?.sessionId) {
        await Session.findByIdAndUpdate(
          cookie.sessionId,
          { isActive: false },
          { new: true }
        );
      } else {
        // If no session ID in cookie, try to find by refresh token
        const hashedToken = crypto
          .createHash("sha256")
          .update(refreshToken)
          .digest("hex");

        await Session.updateOne(
          { token: hashedToken, isActive: true },
          { isActive: false }
        );
      }

      // Log logout event
      logAuthEvent({
        action: "logout",
        user: user,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
          method: "explicit_logout",
        },
      });
    }
  }

  // Return success status
  return res.status(200).json({ message: "Logged out successfully" });
});

/**
 * @desc    Refresh access token using refresh token
 * @route   POST /api/v1/user/refresh-token
 * @access  Public
 */
const handleRefreshToken = asyncHandler(async (req, res) => {
  const userType = "user";
  const refreshTokenCookieName = `${userType}RefreshToken`;

  // Get refresh token from cookies
  const cookies = req.cookies;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;
  const Session = require("../../models/utils/sessionModel");
  const { findSessionByToken } = require("../utils/sessionCtrl");

  // Try to get the refresh token from type-specific cookie first, then fall back to generic
  const refreshToken = cookies[refreshTokenCookieName] || cookies.refreshToken;

  if (!refreshToken) {
    return res.status(401).json({
      message: "No refresh token provided",
      userType,
    });
  }

  try {
    // Verify the refresh token
    if (!process.env.JWT_SECRET) {
      console.error("JWT_SECRET environment variable is not set!");
      return res.status(500).json({ message: "Server configuration error" });
    }

    const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET);

    // Find the user with this refresh token
    const user = await User.findOne({
      _id: decoded.id,
      refreshToken: refreshToken,
    });

    if (!user) {
      // Log failed token refresh
      logAuthEvent({
        action: "token_refresh",
        userId: decoded.id,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Invalid refresh token - token not found in database",
        },
      });

      return res.status(403).json({ message: "Invalid refresh token" });
    }

    // Find the session associated with this token
    const session = await findSessionByToken(refreshToken);

    if (!session) {
      // Log failed token refresh
      logAuthEvent({
        action: "token_refresh",
        user: user,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "No active session found for this token",
        },
      });

      return res.status(403).json({ message: "No active session found" });
    }

    // Generate new tokens with user type
    const newAccessToken = generateToken(user._id, userType);
    const newRefreshToken = generateRefreshToken(user._id, userType);

    // Update refresh token in database
    user.refreshToken = newRefreshToken;
    await user.save();

    // Update the session with the new token
    const hashedToken = crypto
      .createHash("sha256")
      .update(newRefreshToken)
      .digest("hex");

    await Session.findByIdAndUpdate(
      session._id,
      {
        token: hashedToken,
        lastActivity: new Date(),
      },
      { new: true }
    );

    // Set type-specific refresh token as HTTP-only cookie
    res.cookie(`${userType}RefreshToken`, newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production", // Use secure in production
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
      maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the JWT expiration)
    });

    // Set type-specific access token as HTTP-only cookie
    res.cookie(`${userType}AccessToken`, newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours (matching the JWT expiration)
    });

    // Also set generic cookies for backward compatibility
    res.cookie("refreshToken", newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
      maxAge: 72 * 60 * 60 * 1000,
    });

    // Set the new access token as a cookie
    res.cookie("accessToken", newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
      maxAge: 24 * 60 * 60 * 1000,
    });

    // Log successful token refresh
    logAuthEvent({
      action: "token_refresh",
      user: user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        sessionId: session._id,
      },
    });

    // Return the new access token
    return res.json({
      accessToken: newAccessToken,
      message: "Token refreshed successfully",
    });
  } catch (error) {
    // If token verification fails, clear all cookies
    // Clear type-specific cookies
    res.clearCookie(`${userType}RefreshToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
    });

    res.clearCookie(`${userType}AccessToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
    });

    res.clearCookie(`${userType}SessionId`, {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
    });

    // Also clear generic cookies for backward compatibility
    res.clearCookie("refreshToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
    });

    res.clearCookie("accessToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
    });

    res.clearCookie("sessionId", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "none" : "strict",
    });

    // Log failed token refresh
    logAuthEvent({
      action: "token_refresh",
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "failure",
      details: {
        timestamp: new Date(),
        reason: "Invalid or expired refresh token",
        error: error.message,
      },
    });

    return res
      .status(403)
      .json({ message: "Invalid or expired refresh token" });
  }
});

const viewProfile = asyncHandler(async (req, res) => {
  const { id } = req.user;
  try {
    const user = await User.findById(id).select("-password");
    res.json(user);
  } catch (error) {
    throw new Error(error);
  }
});

const updateUser = asyncHandler(async (req, res) => {
  const { id } = req.user;
  validateMongoDbId(id);

  const { error } = validateUser(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }

  // Exclude the password field from the req.body object
  const { password, ...updateData } = req.body;
  try {
    const user = await User.findByIdAndUpdate(id, updateData, { new: true });

    // Validate the updated user data against the user schema
    const { error } = validateUser(user);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    res.json({
      message: "User updated successfully",
      user,
    });
  } catch (error) {
    throw new Error(error);
  }
});

const updatePassword = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { currentPassword, newPassword, confirmPassword } = req.body;
  validateMongoDbId(id);

  // Get client IP for logging
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  // Validate required fields
  if (!currentPassword || !newPassword || !confirmPassword) {
    return res.status(400).json({
      success: false,
      message:
        "Current password, new password, and confirm password are required",
    });
  }

  // Check if new password and confirm password match
  if (newPassword !== confirmPassword) {
    return res.status(400).json({
      success: false,
      message: "New password and confirm password do not match",
    });
  }

  // Check if new password is different from current password
  if (currentPassword === newPassword) {
    return res.status(400).json({
      success: false,
      message: "New password must be different from current password",
    });
  }

  try {
    // Find user and verify current password
    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await user.isPasswordMatched(
      currentPassword
    );
    if (!isCurrentPasswordValid) {
      // Log failed password change attempt
      logAuthEvent({
        action: "password_change",
        user: req.user,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        details: {
          timestamp: new Date(),
          reason: "Invalid current password",
        },
        status: "failure",
      });

      return res.status(400).json({
        success: false,
        message: "Current password is incorrect",
      });
    }

    // Hash new password
    const salt = bcrypt.genSaltSync(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    const updatedUser = await User.findByIdAndUpdate(
      id,
      {
        password: hashedPassword,
        passwordChangedAt: new Date(),
      },
      { new: true }
    ).select("-password -refreshToken");

    // Log successful password change event
    logAuthEvent({
      action: "password_change",
      user: req.user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        timestamp: new Date(),
        method: "user_initiated",
      },
      status: "success",
    });

    res.json({
      success: true,
      message: "Password updated successfully",
      user: updatedUser,
    });
  } catch (error) {
    // Log failed password change
    logAuthEvent({
      action: "password_change",
      user: req.user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        timestamp: new Date(),
        error: error.message,
      },
      status: "failure",
    });

    res.status(500).json({
      success: false,
      message: "Error updating password",
      details: error.message,
    });
  }
});

const deleteAccount = asyncHandler(async (req, res) => {
  const { id } = req.user;

  // Get client IP for logging
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  try {
    // Store user data before deletion for logging
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    // Log account deletion event before actually deleting
    logAuthEvent({
      action: "account_deletion",
      user: user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        reason: req.body.reason || "User requested account deletion",
        timestamp: new Date(),
      },
      status: "success",
    });

    // Now delete the account
    await User.findByIdAndDelete(id);

    res.json({
      message: "Account deleted successfully",
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
      },
    });
  } catch (error) {
    // Log failed account deletion
    logAuthEvent({
      action: "account_deletion",
      user: req.user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        error: error.message,
        timestamp: new Date(),
      },
      status: "failure",
    });

    throw new Error(error);
  }
});

const forgotPasswordToken = asyncHandler(async (req, res) => {
  const { email } = req.body;

  // Get client IP for logging
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  const user = await User.findOne({ email: email });

  if (!user) {
    // Log failed password reset request
    logAuthEvent({
      action: "password_reset_request",
      email,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        reason: "Email not found",
        timestamp: new Date(),
      },
      status: "failure",
    });

    return res.status(404).json({
      message: "User not found with this email",
    });
  }

  try {
    const token = await user.createResetPasswordToken();
    await user.save();

    // Log successful password reset request
    logAuthEvent({
      action: "password_reset_request",
      user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        timestamp: new Date(),
      },
      status: "success",
    });

    // Use environment variable for the base URL or fallback to a default
    const baseUrl = process.env.CLIENT_BASE_URL || "http://localhost:3000";
    const appName = process.env.APP_NAME || "OnPrintz";

    // Create a more professional HTML email template
    const htmlTemplate = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .button { display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px;
                 text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { font-size: 12px; color: #777; margin-top: 30px; text-align: center; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>${appName} - Password Reset</h2>
        </div>
        <div class="content">
          <p>Hello,</p>
          <p>We received a request to reset your password. Please click the button below to create a new password:</p>
          <p style="text-align: center;">
            <a href="${baseUrl}/reset-password/${token}" class="button">Reset Password</a>
          </p>
          <p>This link is valid for 10 minutes from now.</p>
          <p>If you didn't request a password reset, you can safely ignore this email.</p>
          <p>Regards,<br>The ${appName} Team</p>
        </div>
        <div class="footer">
          <p>This is an automated email. Please do not reply to this message.</p>
        </div>
      </div>
    </body>
    </html>
    `;

    // Plain text version for email clients that don't support HTML
    const textVersion = `
    ${appName} - Password Reset

    Hello,

    We received a request to reset your password. Please visit the link below to create a new password:

    ${baseUrl}/reset-password/${token}

    This link is valid for 10 minutes from now.

    If you didn't request a password reset, you can safely ignore this email.

    Regards,
    The ${appName} Team
    `;

    const data = {
      to: email,
      subject: `${appName} - Password Reset Request`,
      text: textVersion,
      htm: htmlTemplate,
    };
    sendEmail(data);
    res.json(token);
  } catch (error) {
    // Log error in password reset
    logAuthEvent({
      action: "password_reset_request",
      user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        error: error.message,
        timestamp: new Date(),
      },
      status: "failure",
    });

    throw new Error(error);
  }
});

const resetPassword = asyncHandler(async (req, res) => {
  const { password } = req.body;
  const { token } = req.params;

  // Get client IP for logging
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  const hashedToken = crypto.createHash("sha256").update(token).digest("hex");
  const user = await User.findOne({
    passwordResetToken: hashedToken,
    passwordResetExpires: { $gt: Date.now() },
  });

  if (!user) {
    // Log failed password reset
    logAuthEvent({
      action: "password_reset_success",
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        reason: "Token expired or invalid",
        timestamp: new Date(),
      },
      status: "failure",
    });

    return res.status(400).json({
      message: "Token Expired, please try again later",
    });
  }

  try {
    // Update user password
    user.password = password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();

    // Log successful password reset
    logAuthEvent({
      action: "password_reset_success",
      user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        timestamp: new Date(),
      },
      status: "success",
    });

    res.json({
      message: "Password reset successful",
      user: {
        id: user._id,
        email: user.email,
      },
    });
  } catch (error) {
    // Log error in password reset
    logAuthEvent({
      action: "password_reset_success",
      user,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        error: error.message,
        timestamp: new Date(),
      },
      status: "failure",
    });

    throw new Error(error);
  }
});

const toggleDarkMode = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { mode } = req.body.preference;
  try {
    const darkmode = await User.findByIdAndUpdate(
      id,
      { "preference.mode": mode },
      {
        new: true,
        runValidators: true, // Optional: Ensure that validators are run
      }
    ).select("preference.mode -_id");
    console.log(darkmode);
    res.json(darkmode);
  } catch (error) {
    throw new Error(error);
  }
});

const updateProfile = asyncHandler(async (req, res) => {
  const { id } = req.user;
  validateMongoDbId(id);

  const form = new formidable.IncomingForm();
  form.keepExtensions = true;

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({
        success: false,
        error: "File parsing error",
        details: err.message,
      });
    }

    try {
      // Prepare update data
      const updateData = {};

      // Handle basic fields
      if (fields.name) {
        updateData.fullname = Array.isArray(fields.name)
          ? fields.name[0]
          : fields.name;
      }

      if (fields.mobile) {
        updateData.mobile = Array.isArray(fields.mobile)
          ? fields.mobile[0]
          : fields.mobile;
      }

      // Handle preference
      if (fields.preference) {
        try {
          const preferenceStr = Array.isArray(fields.preference)
            ? fields.preference[0]
            : fields.preference;
          updateData.preference = JSON.parse(preferenceStr);
        } catch (error) {
          return res.status(400).json({
            success: false,
            message: "Invalid preference format",
            details: error.message,
          });
        }
      }

      // Handle profile image
      if (files.profile) {
        const profileFile = Array.isArray(files.profile)
          ? files.profile[0]
          : files.profile;

        try {
          // Upload to cloudinary
          const result = await cloudinary.uploader.upload(
            profileFile.filepath,
            {
              folder: "profiles",
              resource_type: "image",
            }
          );

          if (result) {
            updateData.profile = result.secure_url;
          }
        } catch (uploadError) {
          console.error("Profile image upload error:", uploadError);
          return res.status(500).json({
            success: false,
            message: "Failed to upload profile image",
            details: uploadError.message,
          });
        }
      }

      // Update user
      const updatedUser = await User.findByIdAndUpdate(id, updateData, {
        new: true,
      }).select("-password -refreshToken");

      if (!updatedUser) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        });
      }

      res.json({
        success: true,
        message: "Profile updated successfully",
        name: updatedUser.fullname,
        username: updatedUser.username,
        email: updatedUser.email,
        mobile: updatedUser.mobile,
        preference: updatedUser.preference,
        profile: updatedUser.profile,
        _id: updatedUser._id,
      });
    } catch (error) {
      console.error("Profile update error:", error);
      res.status(500).json({
        success: false,
        message: "Error updating profile",
        details: error.message,
      });
    }
  });
});

module.exports = {
  validateUserRegister,
  registerUser,
  loginUser,
  logout,
  viewProfile,
  updateUser,
  updatePassword,
  deleteAccount,
  forgotPasswordToken,
  resetPassword,
  toggleDarkMode,
  updateProfile,
  handleRefreshToken,
};
