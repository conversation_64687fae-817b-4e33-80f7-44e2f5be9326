const User = require("../../models/users/userModel");
const ProductType = require("../../models/product/productTypeModel");
const Product = require("../../models/product/productModel");
const QRCode = require("qrcode");
const CryptoJS = require("crypto-js");
const asyncHandler = require("express-async-handler");
const validateMongoDbId = require("../../utils/validateMongoDbId");

const slugify = require("slugify");
const { default: mongoose } = require("mongoose");
const { cloudinaryUploadImg } = require("../../utils/cloudinary");
const fs = require("fs");
const formidable = require("formidable");
const cloudinary = require("cloudinary").v2;
const productCacheService = require("../../services/productCacheService");

const createProduct = asyncHandler(async (req, res) => {
  const form = new formidable.IncomingForm();
  form.multiples = true; // Allow multiple file uploads

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({ error: "File parsing error." });
    }

    console.log(files);

    const productData = {
      title: Array.isArray(fields.title) ? fields.title[0] : fields.title,
      description: Array.isArray(fields.description)
        ? fields.description[0]
        : fields.description,
      basePrice: Array.isArray(fields.basePrice)
        ? fields.basePrice[0]
        : fields.basePrice,
      slug: slugify(
        Array.isArray(fields.title) ? fields.title[0] : fields.title
      ),
      drawWidth: Array.isArray(fields.drawWidth)
        ? fields.drawWidth[0]
        : fields.drawWidth,
      drawHeight: Array.isArray(fields.drawHeight)
        ? fields.drawHeight[0]
        : fields.drawHeight,
      minimumQuantity: Array.isArray(fields.minimumQuantity)
        ? fields.minimumQuantity[0]
        : fields.minimumQuantity || 1,
      color: JSON.parse(fields.color),
      sizes: JSON.parse(fields.sizes),
    };

    // Handle percentage-based canvas positioning if provided (legacy fields)
    if (fields.canvasWidthPercent) {
      productData.canvasWidthPercent = Array.isArray(fields.canvasWidthPercent)
        ? fields.canvasWidthPercent[0]
        : fields.canvasWidthPercent;
    }

    if (fields.canvasHeightPercent) {
      productData.canvasHeightPercent = Array.isArray(
        fields.canvasHeightPercent
      )
        ? fields.canvasHeightPercent[0]
        : fields.canvasHeightPercent;
    }

    if (fields.canvasOffsetXPercent) {
      productData.canvasOffsetXPercent = Array.isArray(
        fields.canvasOffsetXPercent
      )
        ? fields.canvasOffsetXPercent[0]
        : fields.canvasOffsetXPercent;
    }

    if (fields.canvasOffsetYPercent) {
      productData.canvasOffsetYPercent = Array.isArray(
        fields.canvasOffsetYPercent
      )
        ? fields.canvasOffsetYPercent[0]
        : fields.canvasOffsetYPercent;
    }

    // Handle front canvas settings if provided
    if (fields.frontCanvas) {
      try {
        const frontCanvasData = Array.isArray(fields.frontCanvas)
          ? JSON.parse(fields.frontCanvas[0])
          : JSON.parse(fields.frontCanvas);

        productData.frontCanvas = frontCanvasData;
      } catch (error) {
        console.error("Error parsing frontCanvas data:", error);
      }
    }

    // Handle back canvas settings if provided
    if (fields.backCanvas) {
      try {
        const backCanvasData = Array.isArray(fields.backCanvas)
          ? JSON.parse(fields.backCanvas[0])
          : JSON.parse(fields.backCanvas);

        productData.backCanvas = backCanvasData;
      } catch (error) {
        console.error("Error parsing backCanvas data:", error);
      }
    }

    const frontImages = Array.isArray(files.imageFront)
      ? files.imageFront
      : [files.imageFront];

    const backImages = Array.isArray(files.imageBack)
      ? files.imageBack
      : files.imageBack
      ? [files.imageBack]
      : [];

    if (!frontImages.length) {
      return res.status(400).json({ error: "No front images uploaded." });
    }

    try {
      // Upload front images and get their dimensions
      const frontImageResults = await Promise.all(
        frontImages.map(async (image) => {
          const result = await cloudinary.uploader.upload(image.filepath, {
            folder: "products/front",
            // We don't need eager transformations, Cloudinary returns image dimensions by default
          });

          if (result) {
            return {
              url: result.url,
              width: result.width,
              height: result.height,
            };
          } else {
            throw new Error("Front image upload failed");
          }
        })
      );

      // Upload back images if any
      let backImageResults = [];
      if (backImages.length > 0) {
        backImageResults = await Promise.all(
          backImages.map(async (image) => {
            const result = await cloudinary.uploader.upload(image.filepath, {
              folder: "products/back",
              // We don't need eager transformations, Cloudinary returns image dimensions by default
            });

            if (result) {
              return {
                url: result.url,
                width: result.width,
                height: result.height,
              };
            } else {
              throw new Error("Back image upload failed");
            }
          })
        );
      }

      // Store the front image URL and its dimensions
      const frontImageData = frontImageResults[0]; // Assuming the first image is the front image
      productData.imageFront = frontImageData.url;
      productData.originalImageWidth = frontImageData.width;
      productData.originalImageHeight = frontImageData.height;

      // Store the back image URL if available
      if (backImageResults.length > 0) {
        productData.imageBack = backImageResults[0].url;
      }

      // If percentage-based canvas dimensions aren't provided, calculate them based on drawWidth/drawHeight
      if (
        productData.drawWidth &&
        productData.drawHeight &&
        frontImageData.width &&
        frontImageData.height
      ) {
        if (!productData.canvasWidthPercent) {
          productData.canvasWidthPercent =
            (productData.drawWidth / frontImageData.width) * 100;
        }
        if (!productData.canvasHeightPercent) {
          productData.canvasHeightPercent =
            (productData.drawHeight / frontImageData.height) * 100;
        }
      }

      const product = await Product.create(productData);

      // Invalidate product caches after creation
      await productCacheService.invalidateProductCaches(
        product._id,
        productData
      );

      const qrData = JSON.stringify({
        app: "Onprintz",
        id: product._id,
        images: product.imageFront,
        imageB: product.imageBack,
      });

      const encryptedData = CryptoJS.AES.encrypt(qrData, "hello").toString();
      const qrCode = await QRCode.toDataURL(encryptedData);

      res.status(201).json({ product, qrCode });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
});

const getAllProducts = asyncHandler(async (req, res) => {
  try {
    // Try to get from cache first
    const cachedProducts = await productCacheService.cacheAllProducts();

    if (cachedProducts) {
      console.log("🎯 Serving all products from cache");
      return res.json(cachedProducts);
    }

    // Fallback to database if cache fails
    console.log("⚠️ Cache miss, fetching all products from database");
    const products = await Product.find()
      .sort({ displayOrder: 1 })
      .populate("product_category")
      .populate("product_type")
      .populate("color")
      .populate("sizes");

    res.json(products);
  } catch (error) {
    console.error("Error in getAllProducts:", error);
    throw new Error(error);
  }
});

const getAllActiveProducts = asyncHandler(async (req, res) => {
  try {
    // Try to get from cache first
    const cachedProducts = await productCacheService.cacheActiveProducts();

    if (cachedProducts) {
      console.log("🎯 Serving active products from cache");
      return res.json(cachedProducts);
    }

    // Fallback to database if cache fails
    console.log("⚠️ Cache miss, fetching active products from database");
    const products = await Product.find({ status: "active" })
      .sort({ displayOrder: 1 })
      .populate("product_category")
      .populate("product_type")
      .populate("color")
      .populate("sizes");

    res.json(products);
  } catch (error) {
    console.error("Error in getAllActiveProducts:", error);
    throw new Error(error);
  }
});

const getProduct = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    // Validate MongoDB ID
    validateMongoDbId(id);

    // Try to get from cache first
    const cachedProduct = await productCacheService.cacheProductById(id);

    if (cachedProduct) {
      console.log(`🎯 Serving product ${id} from cache`);
      return res.json(cachedProduct);
    }

    // Fallback to database if cache fails
    console.log(`⚠️ Cache miss, fetching product ${id} from database`);
    const product = await Product.findById(id)
      .populate("product_category")
      .populate("product_type")
      .populate("color")
      .populate("sizes");

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    res.json(product);
  } catch (error) {
    console.error("Error in getProduct:", error);
    throw new Error(error);
  }
});

const updateProduct = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if this is a multipart form (file upload) or regular JSON
  const contentType = req.headers["content-type"];
  const isMultipart =
    contentType && contentType.includes("multipart/form-data");

  if (isMultipart) {
    // Handle file upload using formidable (similar to createProduct)
    const form = new formidable.IncomingForm();
    form.multiples = true;

    form.parse(req, async (err, fields, files) => {
      if (err) {
        return res.status(400).json({ error: "File parsing error." });
      }

      try {
        // Get the current product first
        const currentProduct = await Product.findById(id);
        if (!currentProduct) {
          return res.status(404).json({ error: "Product not found." });
        }

        // Prepare update data from fields
        const updateData = {};

        // Handle text fields
        if (fields.title) {
          updateData.title = Array.isArray(fields.title)
            ? fields.title[0]
            : fields.title;
          updateData.slug = slugify(updateData.title);
        }
        if (fields.description) {
          updateData.description = Array.isArray(fields.description)
            ? fields.description[0]
            : fields.description;
        }
        if (fields.basePrice) {
          updateData.basePrice = Array.isArray(fields.basePrice)
            ? fields.basePrice[0]
            : fields.basePrice;
        }
        if (fields.cost) {
          updateData.cost = Array.isArray(fields.cost)
            ? fields.cost[0]
            : fields.cost;
        }
        if (fields.defaultCustomizationPrice) {
          updateData.defaultCustomizationPrice = Array.isArray(
            fields.defaultCustomizationPrice
          )
            ? fields.defaultCustomizationPrice[0]
            : fields.defaultCustomizationPrice;
        }
        if (fields.frontCustomizationPrice) {
          updateData.frontCustomizationPrice = Array.isArray(
            fields.frontCustomizationPrice
          )
            ? fields.frontCustomizationPrice[0]
            : fields.frontCustomizationPrice;
        }
        if (fields.backCustomizationPrice) {
          updateData.backCustomizationPrice = Array.isArray(
            fields.backCustomizationPrice
          )
            ? fields.backCustomizationPrice[0]
            : fields.backCustomizationPrice;
        }
        if (fields.minimumQuantity) {
          updateData.minimumQuantity = Array.isArray(fields.minimumQuantity)
            ? fields.minimumQuantity[0]
            : fields.minimumQuantity;
        }
        if (fields.color) {
          updateData.color = JSON.parse(
            Array.isArray(fields.color) ? fields.color[0] : fields.color
          );
        }
        if (fields.sizes) {
          updateData.sizes = JSON.parse(
            Array.isArray(fields.sizes) ? fields.sizes[0] : fields.sizes
          );
        }
        if (fields.product_type) {
          updateData.product_type = Array.isArray(fields.product_type)
            ? fields.product_type[0]
            : fields.product_type;
        }

        // Handle legacy fields
        if (fields.drawWidth) {
          updateData.drawWidth = Array.isArray(fields.drawWidth)
            ? fields.drawWidth[0]
            : fields.drawWidth;
        }
        if (fields.drawHeight) {
          updateData.drawHeight = Array.isArray(fields.drawHeight)
            ? fields.drawHeight[0]
            : fields.drawHeight;
        }
        if (fields.canvasWidthPercent) {
          updateData.canvasWidthPercent = Array.isArray(
            fields.canvasWidthPercent
          )
            ? fields.canvasWidthPercent[0]
            : fields.canvasWidthPercent;
        }
        if (fields.canvasHeightPercent) {
          updateData.canvasHeightPercent = Array.isArray(
            fields.canvasHeightPercent
          )
            ? fields.canvasHeightPercent[0]
            : fields.canvasHeightPercent;
        }
        if (fields.canvasOffsetXPercent) {
          updateData.canvasOffsetXPercent = Array.isArray(
            fields.canvasOffsetXPercent
          )
            ? fields.canvasOffsetXPercent[0]
            : fields.canvasOffsetXPercent;
        }
        if (fields.canvasOffsetYPercent) {
          updateData.canvasOffsetYPercent = Array.isArray(
            fields.canvasOffsetYPercent
          )
            ? fields.canvasOffsetYPercent[0]
            : fields.canvasOffsetYPercent;
        }

        // Handle front canvas settings if provided
        if (fields.frontCanvas) {
          try {
            const frontCanvasData = Array.isArray(fields.frontCanvas)
              ? JSON.parse(fields.frontCanvas[0])
              : JSON.parse(fields.frontCanvas);
            updateData.frontCanvas = frontCanvasData;
          } catch (error) {
            console.error("Error parsing frontCanvas data:", error);
          }
        }

        // Handle back canvas settings if provided
        if (fields.backCanvas) {
          try {
            const backCanvasData = Array.isArray(fields.backCanvas)
              ? JSON.parse(fields.backCanvas[0])
              : JSON.parse(fields.backCanvas);
            updateData.backCanvas = backCanvasData;
          } catch (error) {
            console.error("Error parsing backCanvas data:", error);
          }
        }

        // Handle image uploads
        let frontImageUploaded = false;
        let backImageUploaded = false;

        // Handle front image upload
        if (files.imageFront) {
          const frontImages = Array.isArray(files.imageFront)
            ? files.imageFront
            : [files.imageFront];

          if (frontImages[0] && frontImages[0].size > 0) {
            // Delete old front image from cloudinary if it exists
            if (
              currentProduct.imageFront &&
              currentProduct.imageFront.includes("cloudinary.com")
            ) {
              try {
                const frontImagePublicId = currentProduct.imageFront
                  .split("/")
                  .pop()
                  .split(".")[0];
                await cloudinary.uploader.destroy(
                  `products/front/${frontImagePublicId}`
                );
              } catch (deleteError) {
                console.error("Error deleting old front image:", deleteError);
              }
            }

            // Upload new front image
            const result = await cloudinary.uploader.upload(
              frontImages[0].filepath,
              {
                folder: "products/front",
              }
            );

            if (result) {
              updateData.imageFront = result.url;
              updateData.originalImageWidth = result.width;
              updateData.originalImageHeight = result.height;
              frontImageUploaded = true;
            }
          }
        }

        // Handle back image upload
        if (files.imageBack) {
          const backImages = Array.isArray(files.imageBack)
            ? files.imageBack
            : [files.imageBack];

          if (backImages[0] && backImages[0].size > 0) {
            // Delete old back image from cloudinary if it exists
            if (
              currentProduct.imageBack &&
              currentProduct.imageBack.includes("cloudinary.com")
            ) {
              try {
                const backImagePublicId = currentProduct.imageBack
                  .split("/")
                  .pop()
                  .split(".")[0];
                await cloudinary.uploader.destroy(
                  `products/back/${backImagePublicId}`
                );
              } catch (deleteError) {
                console.error("Error deleting old back image:", deleteError);
              }
            }

            // Upload new back image
            const result = await cloudinary.uploader.upload(
              backImages[0].filepath,
              {
                folder: "products/back",
              }
            );

            if (result) {
              updateData.imageBack = result.url;
              backImageUploaded = true;
            }
          }
        }

        // Calculate percentage values if new images were uploaded or dimensions changed
        if (
          frontImageUploaded ||
          updateData.drawWidth ||
          updateData.drawHeight
        ) {
          const imageWidth =
            updateData.originalImageWidth || currentProduct.originalImageWidth;
          const imageHeight =
            updateData.originalImageHeight ||
            currentProduct.originalImageHeight;

          if (imageWidth && imageHeight) {
            if (updateData.drawWidth && !updateData.canvasWidthPercent) {
              updateData.canvasWidthPercent =
                (updateData.drawWidth / imageWidth) * 100;
            }
            if (updateData.drawHeight && !updateData.canvasHeightPercent) {
              updateData.canvasHeightPercent =
                (updateData.drawHeight / imageHeight) * 100;
            }
          }
        }

        // Update the product
        const updatedProduct = await Product.findByIdAndUpdate(id, updateData, {
          new: true,
        });

        // Invalidate product caches after update
        await productCacheService.invalidateProductCaches(id, updateData);

        res.status(200).json(updatedProduct);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  } else {
    // Handle regular JSON update (existing functionality)
    try {
      // If title is being updated, update the slug as well
      if (req.body.title) {
        req.body.slug = slugify(req.body.title);
      }

      // If drawWidth and drawHeight are being updated but not the percentage values,
      // calculate the percentage values based on the original image dimensions
      if (
        (req.body.drawWidth || req.body.drawHeight) &&
        (!req.body.canvasWidthPercent || !req.body.canvasHeightPercent)
      ) {
        // Get the current product to access original image dimensions
        const currentProduct = await Product.findById(id);

        if (
          currentProduct &&
          currentProduct.originalImageWidth &&
          currentProduct.originalImageHeight
        ) {
          // Calculate percentage values if not provided
          if (req.body.drawWidth && !req.body.canvasWidthPercent) {
            req.body.canvasWidthPercent =
              (req.body.drawWidth / currentProduct.originalImageWidth) * 100;
          }

          if (req.body.drawHeight && !req.body.canvasHeightPercent) {
            req.body.canvasHeightPercent =
              (req.body.drawHeight / currentProduct.originalImageHeight) * 100;
          }
        }
      }

      // Handle front canvas settings if provided
      if (req.body.frontCanvas && typeof req.body.frontCanvas === "string") {
        try {
          req.body.frontCanvas = JSON.parse(req.body.frontCanvas);
        } catch (error) {
          console.error("Error parsing frontCanvas data:", error);
        }
      }

      // Handle back canvas settings if provided
      if (req.body.backCanvas && typeof req.body.backCanvas === "string") {
        try {
          req.body.backCanvas = JSON.parse(req.body.backCanvas);
        } catch (error) {
          console.error("Error parsing backCanvas data:", error);
        }
      }

      // Update the product with the new values
      const product = await Product.findByIdAndUpdate(id, req.body, {
        new: true,
      });

      // Invalidate product caches after update
      await productCacheService.invalidateProductCaches(id, req.body);

      res.status(200).json(product);
    } catch (error) {
      throw new Error(error);
    }
  }
});

const deleteProduct = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    // First find the product to get the image URLs
    const product = await Product.findById(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    // Import cloudinary utilities
    const cloudinary = require("cloudinary").v2;

    // Track deletion results
    const deletionResults = {
      success: [],
      failed: [],
    };

    // Handle front image deletion
    if (product.imageFront && product.imageFront.includes("cloudinary.com")) {
      try {
        console.log(`Attempting to delete front image: ${product.imageFront}`);

        // Extract the public ID from the URL
        const frontImagePublicId = product.imageFront
          .split("/")
          .pop()
          .split(".")[0];

        // Delete from the products/front folder
        const result = await cloudinary.uploader.destroy(
          `products/front/${frontImagePublicId}`
        );

        if (result && result.result === "ok") {
          deletionResults.success.push(product.imageFront);
          console.log(
            `Successfully deleted front image: ${product.imageFront}`
          );
        } else {
          deletionResults.failed.push(product.imageFront);
          console.log(`Failed to delete front image: ${product.imageFront}`);
        }
      } catch (err) {
        console.error(`Error deleting front image ${product.imageFront}:`, err);
        deletionResults.failed.push(product.imageFront);
      }
    }

    // Handle back image deletion (only if it exists)
    if (product.imageBack && product.imageBack.includes("cloudinary.com")) {
      try {
        console.log(`Attempting to delete back image: ${product.imageBack}`);

        // Extract the public ID from the URL
        const backImagePublicId = product.imageBack
          .split("/")
          .pop()
          .split(".")[0];

        // Delete from the products/back folder
        const result = await cloudinary.uploader.destroy(
          `products/back/${backImagePublicId}`
        );

        if (result && result.result === "ok") {
          deletionResults.success.push(product.imageBack);
          console.log(`Successfully deleted back image: ${product.imageBack}`);
        } else {
          deletionResults.failed.push(product.imageBack);
          console.log(`Failed to delete back image: ${product.imageBack}`);
        }
      } catch (err) {
        console.error(`Error deleting back image ${product.imageBack}:`, err);
        deletionResults.failed.push(product.imageBack);
      }
    }

    // Log the deletion results
    console.log(`Image deletion results:`, {
      success: deletionResults.success.length,
      failed: deletionResults.failed.length,
    });

    // Delete the product from the database
    const deletedProduct = await Product.findByIdAndDelete(id);

    // Invalidate product caches after deletion
    await productCacheService.invalidateProductCaches(id, product);

    res.status(200).json({
      success: true,
      message: "Product deleted successfully",
      product: deletedProduct,
      imagesDeletion: {
        successCount: deletionResults.success.length,
        failedCount: deletionResults.failed.length,
        successUrls: deletionResults.success,
        failedUrls: deletionResults.failed,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting product",
      error: error.message,
    });
  }
});

const uploadImages = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const uploader = (path) => cloudinaryUploadImg(path, "images");
    const urls = [];
    const files = req.files;
    for (const file of files) {
      const { path } = file;
      const newpath = await uploader(path);
      console.log(newpath);
      urls.push(newpath);
      // fs.unlinkSync(path);
    }
    const findProduct = await Product.findByIdAndUpdate(
      id,
      {
        images: urls.map((file) => {
          return file;
        }),
      },
      { new: true }
    );

    // Invalidate product caches after image upload
    try {
      await productCacheService.invalidateProductCaches(id, {
        images: urls,
        operation: "imageUpload",
      });
      console.log(
        `🧹 Product caches invalidated after image upload for product: ${id}`
      );
    } catch (cacheError) {
      console.error(
        "Error invalidating product caches after image upload:",
        cacheError
      );
      // Continue with the main flow regardless of cache errors
    }

    res.json(findProduct);
  } catch (error) {
    throw new Error(error);
  }
});

const toggleProductStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    const product = await Product.findById(id);
    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    console.log(
      `🔄 Toggling product status for ${id}: ${product.status} -> ${
        product.status === "active" ? "inactive" : "active"
      }`
    );

    // Toggle the status between active and inactive
    const newStatus = product.status === "active" ? "inactive" : "active";

    const updatedProduct = await Product.findByIdAndUpdate(
      id,
      { status: newStatus },
      { new: true }
    );

    console.log(
      `✅ Product status updated in database: ${updatedProduct.status}`
    );

    // Invalidate product caches after status change
    try {
      await productCacheService.invalidateProductCaches(id, {
        status: newStatus,
        previousStatus: product.status,
        operation: "statusToggle",
      });
      console.log(
        `🧹 Product caches invalidated after status toggle for product: ${id}`
      );

      // Double-check that filtered caches are actually cleared
      console.log(`🔍 Double-checking filtered cache clearance...`);
      await productCacheService.clearAllFilteredCaches();

      // Also clear the specific common cache key that's likely being used
      console.log(`🔍 Clearing specific common filtered cache keys...`);
      await productCacheService.clearCommonFilteredCaches();
    } catch (cacheError) {
      console.error(
        "Error invalidating product caches after status toggle:",
        cacheError
      );
      // Continue with the main flow regardless of cache errors
    }

    res.status(200).json(updatedProduct);
  } catch (error) {
    console.error("Error in toggleProductStatus:", error);
    throw new Error(error);
  }
});

const updateProductsOrder = asyncHandler(async (req, res) => {
  const { productOrders } = req.body;
  try {
    // Use Promise.all to update all products concurrently
    const updatePromises = productOrders.map((item) => {
      return Product.findByIdAndUpdate(
        item.id,
        { displayOrder: item.order },
        { new: true }
      );
    });

    await Promise.all(updatePromises);

    // Invalidate product caches after order update
    // Since display order affects all product listings, we need to clear all caches
    try {
      await productCacheService.invalidateProductCaches("bulk_order_update", {
        affectedProducts: productOrders.map((item) => item.id),
        operation: "displayOrder",
      });
      console.log("🧹 Product caches invalidated after order update");
    } catch (cacheError) {
      console.error(
        "Error invalidating product caches after order update:",
        cacheError
      );
      // Continue with the main flow regardless of cache errors
    }

    res
      .status(200)
      .json({ success: true, message: "Product order updated successfully" });
  } catch (error) {
    throw new Error(error);
  }
});

// Advanced filtering endpoint with caching
const getFilteredProducts = asyncHandler(async (req, res) => {
  try {
    const filters = {
      search: req.query.search,
      category: req.query.category,
      type: req.query.type,
      colors: req.query.colors,
      sizes: req.query.sizes,
      minPrice: req.query.minPrice,
      maxPrice: req.query.maxPrice,
      sortBy: req.query.sortBy || "displayOrder",
      sortOrder: req.query.sortOrder || "asc",
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 12,
      status: req.query.status || "active",
    };

    console.log(`🔍 Filtered products request with filters:`, filters);

    // Try to get from cache first
    const cachedResult = await productCacheService.cacheFilteredProducts(
      filters
    );

    if (cachedResult) {
      console.log("🎯 Serving filtered products from cache");
      console.log(`📊 Cache data timestamp: ${cachedResult.generatedAt}`);
      console.log(
        `📊 Cache data product count: ${cachedResult.products?.length}`
      );
      return res.json(cachedResult);
    }

    // Fallback to database if cache fails
    console.log("⚠️ Cache miss, fetching filtered products from database");

    const {
      search,
      category,
      type,
      colors,
      sizes,
      minPrice,
      maxPrice,
      sortBy,
      sortOrder,
      page,
      limit,
      status,
    } = filters;

    // Build the filter query
    const filter = { status };

    // Search functionality - primarily based on product title
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
        { "product_category.category_name": { $regex: search, $options: "i" } },
        { "product_type.productName": { $regex: search, $options: "i" } },
      ];
    }

    // Category filter
    if (category) {
      filter.product_category = category;
    }

    // Type filter
    if (type) {
      filter.product_type = type;
    }

    // Colors filter
    if (colors) {
      const colorArray = Array.isArray(colors) ? colors : colors.split(",");
      filter.color = { $in: colorArray };
    }

    // Sizes filter
    if (sizes) {
      const sizeArray = Array.isArray(sizes) ? sizes : sizes.split(",");
      filter.sizes = { $in: sizeArray };
    }

    // Price range filter
    if (minPrice || maxPrice) {
      filter.basePrice = {};
      if (minPrice) filter.basePrice.$gte = parseFloat(minPrice);
      if (maxPrice) filter.basePrice.$lte = parseFloat(maxPrice);
    }

    // Sorting
    const sortOptions = {};
    const validSortFields = [
      "displayOrder",
      "title",
      "basePrice",
      "createdAt",
      "sold",
    ];
    const sortField = validSortFields.includes(sortBy)
      ? sortBy
      : "displayOrder";
    const order = sortOrder === "desc" ? -1 : 1;
    sortOptions[sortField] = order;

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Execute query with population
    const products = await Product.find(filter)
      .populate("product_category")
      .populate("product_type")
      .populate("color")
      .populate("sizes")
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum);

    // Get total count for pagination
    const totalProducts = await Product.countDocuments(filter);
    const totalPages = Math.ceil(totalProducts / limitNum);

    res.json({
      products,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalProducts,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1,
      },
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Get filter options for the frontend with caching
const getFilterOptions = asyncHandler(async (req, res) => {
  try {
    // Try to get from cache first
    const cachedOptions = await productCacheService.cacheFilterOptions();

    if (cachedOptions) {
      console.log("🎯 Serving filter options from cache");
      return res.json(cachedOptions);
    }

    // Fallback to database if cache fails
    console.log("⚠️ Cache miss, fetching filter options from database");

    const [categories, types, colors, sizes, priceRange] = await Promise.all([
      // Get all product categories
      Product.aggregate([
        {
          $match: {
            status: "active",
            product_category: { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "productcategories",
            localField: "product_category",
            foreignField: "_id",
            as: "category",
          },
        },
        { $unwind: { path: "$category", preserveNullAndEmptyArrays: false } },
        {
          $group: {
            _id: "$category._id",
            name: { $first: "$category.category_name" },
            count: { $sum: 1 },
          },
        },
        { $sort: { name: 1 } },
      ]),

      // Get all product types
      Product.aggregate([
        {
          $match: {
            status: "active",
            product_type: { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "producttypes",
            localField: "product_type",
            foreignField: "_id",
            as: "type",
          },
        },
        { $unwind: { path: "$type", preserveNullAndEmptyArrays: false } },
        {
          $group: {
            _id: "$type._id",
            name: { $first: "$type.productName" },
            count: { $sum: 1 },
          },
        },
        { $sort: { name: 1 } },
      ]),

      // Get all colors
      Product.aggregate([
        { $match: { status: "active" } },
        { $unwind: "$color" },
        {
          $lookup: {
            from: "colors",
            localField: "color",
            foreignField: "_id",
            as: "colorInfo",
          },
        },
        { $unwind: "$colorInfo" },
        {
          $group: {
            _id: "$colorInfo._id",
            name: { $first: "$colorInfo.name" },
            hex_code: { $first: "$colorInfo.hex_code" },
            count: { $sum: 1 },
          },
        },
        { $sort: { name: 1 } },
      ]),

      // Get all sizes
      Product.aggregate([
        { $match: { status: "active" } },
        { $unwind: "$sizes" },
        {
          $lookup: {
            from: "sizes",
            localField: "sizes",
            foreignField: "_id",
            as: "sizeInfo",
          },
        },
        { $unwind: "$sizeInfo" },
        {
          $group: {
            _id: "$sizeInfo._id",
            name: { $first: "$sizeInfo.size_name" },
            description: { $first: "$sizeInfo.size_description" },
            count: { $sum: 1 },
          },
        },
        { $sort: { name: 1 } },
      ]),

      // Get price range
      Product.aggregate([
        { $match: { status: "active" } },
        {
          $group: {
            _id: null,
            minPrice: { $min: "$basePrice" },
            maxPrice: { $max: "$basePrice" },
          },
        },
      ]),
    ]);

    res.json({
      categories,
      types,
      colors,
      sizes,
      priceRange: priceRange[0] || { minPrice: 0, maxPrice: 100 },
    });
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  createProduct,
  getAllProducts,
  getAllActiveProducts,
  getProduct,
  updateProduct,
  deleteProduct,
  uploadImages,
  toggleProductStatus,
  updateProductsOrder,
  getFilteredProducts,
  getFilterOptions,
};
