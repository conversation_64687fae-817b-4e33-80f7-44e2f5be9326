const express = require("express");
const router = express.Router();
const {
  getUserImages,
  getUserProducts,
  createUserProduct,
  getLinkProduct,
  deleteProduct,
  updateUserProduct,
  uploadImage,
  updateUserImage,
} = require("../../controllers/other/affiliateCtrl");
const { authMiddleware } = require("../../middlewares/authMiddleware");

router.get("/user-images", authMiddleware, getUserImages);
router.patch("/user-images/:id", authMiddleware, updateUserImage);
router.post("/upload-image", authMiddleware, uploadImage);
router.get("/user-products", authMiddleware, getUserProducts);
router.post("/create-user-product", authMiddleware, createUserProduct);
router.get("/link/:random", getLinkProduct);
router.delete("/delete/:id", deleteProduct);
router.put("/update/:prod", updateUserProduct);
module.exports = router;
